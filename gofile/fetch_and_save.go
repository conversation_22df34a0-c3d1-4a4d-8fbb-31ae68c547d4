package gofile

import (
	"bytes"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"image"
	"image/draw"
	"image/jpeg"

	nativewebp "github.com/HugoSmits86/nativewebp"
	golog "github.com/real-rm/golog"
	xdraw "golang.org/x/image/draw"
)

const (
	DEFAULT_RETRIES = 3
)

// downloadWithRetry attempts to download from a URL with retries
// Returns the response and error if any
// If maxRetries is not provided, uses DEFAULT_RETRIES (1)
func downloadWithRetry(url string, maxRetries ...int) (*http.Response, error) {
	retries := DEFAULT_RETRIES
	if len(maxRetries) > 0 {
		retries = maxRetries[0]
	}

	// Create a custom client with timeout settings
	client := &http.Client{
		Timeout: 120 * time.Second,
		Transport: &http.Transport{
			ResponseHeaderTimeout: 120 * time.Second,
			ExpectContinueTimeout: 10 * time.Second,
			IdleConnTimeout:       60 * time.Second,
			TLSHandshakeTimeout:   60 * time.Second,
			DialContext: (&net.Dialer{
				Timeout:   60 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			DisableKeepAlives:   true,
			MaxIdleConns:        2,
			MaxConnsPerHost:     2,
			MaxIdleConnsPerHost: 2,
			DisableCompression:  true,
			ForceAttemptHTTP2:   false,
		},
	}

	if http.DefaultClient.Timeout > 0 {
		client = http.DefaultClient
	}

	for i := 0; i < retries; i++ {
		resp, err := client.Get(url)
		if err != nil {
			// Check if it's a timeout error
			if ne, ok := err.(net.Error); ok && ne.Timeout() || os.IsTimeout(err) {
				golog.Warn("timeout", "url", url, "attempt", i+1, "err", err)
				if i < retries-1 {
					time.Sleep(time.Second * time.Duration(i+1))
					continue
				}
				return nil, fmt.Errorf("request timed out: %w", err)
			}
			if i < retries-1 {
				golog.Warn("retry downloading",
					"url", url,
					"attempt", i+1,
					"error", err)
				time.Sleep(time.Second * time.Duration(i+1))
				continue
			}
			return nil, fmt.Errorf("failed download: %w", err)
		}

		// At this point, we have a response
		if resp.StatusCode == http.StatusOK {
			return resp, nil
		}

		// Close the response body if we're not returning it
		if closeErr := resp.Body.Close(); closeErr != nil {
			golog.Error("failed to close response body", "error", closeErr)
		}

		if i < retries-1 {
			golog.Warn("retry downloading",
				"url", url,
				"attempt", i+1,
				"status", resp.StatusCode)
			time.Sleep(time.Second * time.Duration(i+1))
		}
	}

	return nil, fmt.Errorf("failed after %d attempts", retries)
}

// saveAsJPEG saves the image as JPEG format
// path is the full path to the file including filename and extension
func saveAsJPEG(img image.Image, path string) (string, error) {
	if img == nil {
		return "", fmt.Errorf("cannot save nil image")
	}

	// Create directory if it doesn't exist
	dir := filepath.Dir(path)
	if err := MkdirAll(dir, 0755); err != nil {
		return "", fmt.Errorf("failed to create directory: %w", err)
	}

	// Create file
	file, err := Create(path)
	if err != nil {
		golog.Error("failed to create output file", "path", path, "error", err)
		return "", fmt.Errorf("failed to create output file: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			golog.Error("failed to close file", "path", path, "error", err)
		}
	}()

	if err := jpeg.Encode(file, img, &jpeg.Options{Quality: 95}); err != nil {
		golog.Error("failed to encode JPEG", "error", err)
		return "", fmt.Errorf("failed to encode JPEG: %w", err)
	}

	return path, nil
}

// saveAsWebP saves the image as WebP format with compression
// path is the full path to the file including filename and extension
func saveAsWebP(img image.Image, path string) (string, error) {
	if img == nil {
		return "", fmt.Errorf("cannot save nil image")
	}

	// Ensure the file has .webp extension
	ext := filepath.Ext(path)
	if ext != ".webp" {
		path = path[:len(path)-len(ext)] + ".webp"
	}

	// Create directory if it doesn't exist
	dir := filepath.Dir(path)
	if err := MkdirAll(dir, 0750); err != nil {
		return "", fmt.Errorf("failed to create directory: %w", err)
	}

	file, err := Create(path)
	if err != nil {
		golog.Error("failed to create file", "path", path, "error", err)
		return "", fmt.Errorf("failed to create file: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			golog.Error("failed to close file", "path", path, "error", err)
		}
	}()
	err = nativewebp.Encode(file, img, nil)
	if err != nil {
		golog.Error("failed to encode WebP", "error", err)
		return "", fmt.Errorf("failed to encode WebP: %w", err)
	}

	return path, nil
}

// SaveImage saves the provided image data to the specified path
// If compressWebP is true, attempts to convert the image to WebP format with compression
// Falls back to JPEG if WebP encoding is not available
// Returns the final saved file path and any error that occurred
func SaveImage(img image.Image, savePath string, compressWebP bool) (string, error) {
	// Create directory if it doesn't exist
	dir := filepath.Dir(savePath)
	if err := MkdirAll(dir, 0755); err != nil {
		golog.Error("failed to create directory", "error", err)
		return "", fmt.Errorf("failed to create directory: %w", err)
	}

	var finalPath string
	var err error
	if compressWebP {
		// Try to save as WebP
		finalPath, err = saveAsWebP(img, savePath)
		if err != nil {
			golog.Warn("failed to save as WebP, falling back to JPEG", "error", err)
			// Change extension to .jpg for fallback to avoid file extension mismatch
			ext := filepath.Ext(savePath)
			jpgPath := savePath[:len(savePath)-len(ext)] + ".jpg"
			return saveAsJPEG(img, jpgPath)
		}
	} else {
		// Save as JPEG
		finalPath, err = saveAsJPEG(img, savePath)
		if err != nil {
			return "", err
		}
	}

	golog.Info("Successfully saved image",
		"path", finalPath,
		"compressed", compressWebP)

	return finalPath, nil
}

// DownloadAndSaveImageInDirs downloads an image from URL and saves it to specified paths
// savePaths is a list of full paths including filename and extension
// If compressWebP is true, attempts to convert the image to WebP format with compression
// Falls back to JPEG if WebP encoding is not available
// Returns a map [savePath]savedPath and any error that occurred
func DownloadAndSaveImageInDirs(url string, savePaths []string, compressWebP bool, maxRetries ...int) (map[string]string, error) {
	// Download image with retry
	resp, err := downloadWithRetry(url, maxRetries...)
	if err != nil {
		return nil, fmt.Errorf("failed to download image: %w", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			golog.Error("failed to close response body", "error", err)
		}
	}()

	imgData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read image data: %w", err)
	}

	img, _, err := image.Decode(bytes.NewReader(imgData))
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	results := make(map[string]string)
	for _, savePath := range savePaths {
		var savedPath string
		var saveErr error
		if compressWebP {
			savedPath, saveErr = saveAsWebP(img, savePath)
			if saveErr != nil {
				golog.Warn("failed to save as WebP, falling back to JPEG", "error", saveErr)
				// Change extension to .jpg for fallback
				ext := filepath.Ext(savePath)
				jpgPath := savePath[:len(savePath)-len(ext)] + ".jpg"
				savedPath, saveErr = saveAsJPEG(img, jpgPath)
			}
		} else {
			savedPath, saveErr = saveAsJPEG(img, savePath)
		}

		if saveErr != nil {
			golog.Error("failed to save image", "path", savePath, "error", saveErr)
			continue
		}
		results[savePath] = savedPath
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("failed to save image to any directory")
	}

	return results, nil
}

// DownloadAndResizeImage downloads an image from URL and returns the resized image
// width and height are the target dimensions, the image will maintain its aspect ratio
// and fit within these dimensions
func DownloadAndResizeImage(url string, width, height int) (image.Image, error) {
	// Validate dimensions
	if width <= 0 || height <= 0 {
		return nil, fmt.Errorf("invalid dimensions: width and height must be positive")
	}

	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to download image: %w", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			golog.Error("failed to close response body", "error", err)
		}
	}()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status %d while downloading image", resp.StatusCode)
	}
	// Decode image
	img, _, err := image.Decode(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Resize image
	bounds := img.Bounds()
	imgWidth := bounds.Dx()
	imgHeight := bounds.Dy()

	// Calculate new dimensions maintaining aspect ratio
	var newWidth, newHeight int
	if imgWidth > imgHeight {
		newWidth = width
		newHeight = int(float64(imgHeight) * float64(width) / float64(imgWidth))
	} else {
		newHeight = height
		newWidth = int(float64(imgWidth) * float64(height) / float64(imgHeight))
	}
	// Avoid height or width is 0
	if newHeight == 0 {
		newHeight = 1
	}
	if newWidth == 0 {
		newWidth = 1
	}

	// Create new image
	newImg := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// Scale image using high-quality CatmullRom interpolation
	xdraw.CatmullRom.Scale(newImg, newImg.Bounds(), img, img.Bounds(), draw.Over, nil)

	return newImg, nil
}

// saveNotPic saves a file in its original format
// Returns the path to the saved file and any error that occurred
func saveNotPic(data []byte, savePath string) (string, error) {
	// Create directory if it doesn't exist
	dir := filepath.Dir(savePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", fmt.Errorf("failed to create directory: %w", err)
	}

	// Create output file
	file, err := os.Create(savePath)
	if err != nil {
		return "", fmt.Errorf("failed to create output file: %w", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			golog.Error("failed to close file", "path", savePath, "error", err)
		}
	}()

	// Write data to file
	if _, err := file.Write(data); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	golog.Info("Successfully saved file", "path", savePath)
	return savePath, nil
}

type DownloadAndSaveFileOptions struct {
	URL          string   // URL to download from
	SavePaths    []string // Paths to save the file to
	CompressWebP bool     // Whether to compress as WebP (only for images)
	IsPhoto      bool     // Whether the file is a photo
	MaxRetries   int      // Maximum number of retry attempts
}

// DownloadAndSaveFile downloads a file and saves it to the specified format
// Returns the path to the saved file and any error that occurred
func DownloadAndSaveFile(opts *DownloadAndSaveFileOptions) ([]string, error) {
	if opts == nil {
		return nil, fmt.Errorf("options are nil")
	}
	if opts.MaxRetries == 0 {
		opts.MaxRetries = DEFAULT_RETRIES
	}

	// Download file with retry
	resp, err := downloadWithRetry(opts.URL, opts.MaxRetries)
	if err != nil {
		return nil, fmt.Errorf("failed to download file: %w", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			golog.Error("failed to close response body", "error", err)
		}
	}()

	// Read file data
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read file data: %w", err)
	}
	if len(opts.SavePaths) == 0 {
		return nil, fmt.Errorf("no save paths provided")
	}

	results := make([]string, 0)
	var img image.Image
	if opts.IsPhoto {
		var decodeErr error
		img, _, decodeErr = image.Decode(bytes.NewReader(data))
		if decodeErr != nil {
			return nil, fmt.Errorf("failed to decode image: %w", decodeErr)
		}
	}
	// Save the file
	for _, savePath := range opts.SavePaths {
		var savedPath string
		var saveErr error
		if opts.IsPhoto {
			if opts.CompressWebP {
				savedPath, saveErr = saveAsWebP(img, savePath)
				if saveErr != nil {
					golog.Warn("failed to save as WebP, falling back to JPEG", "error", saveErr)
					// Change extension to .jpg for fallback
					ext := filepath.Ext(savePath)
					jpgPath := savePath[:len(savePath)-len(ext)] + ".jpg"
					savedPath, saveErr = saveAsJPEG(img, jpgPath)
					golog.Debug("savedPath", savedPath, "saveErr", saveErr)
				}
			} else {
				savedPath, saveErr = saveAsJPEG(img, savePath)
			}
			if saveErr != nil {
				golog.Error("failed to save as JPEG", "error", saveErr)
				continue
			}
			golog.Debug("savedPath", savedPath, "saveErr", saveErr)
		} else {
			savedPath, saveErr = saveNotPic(data, savePath)
			if saveErr != nil {
				golog.Error("failed to save file", "path", savePath, "error", saveErr)
				continue
			}
		}
		results = append(results, savedPath)
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("failed to save file to any directory")
	}

	golog.Debug("Successfully downloaded and saved file",
		"path", results,
		"content-type", resp.Header.Get("Content-Type"))

	return results, nil
}
