package gofile

import (
	"bytes"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"io"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/real-rm/gofile/levelStore"
	"github.com/real-rm/gohelper"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTestProcess sets up the test environment
func setupTestProcess(t *testing.T) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}

	gohelper.SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}
}

// Helper functions
func createTestImage(width, height int) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	blue := color.RGBA{B: 255, A: 255}
	draw.Draw(img, img.Bounds(), &image.Uniform{blue}, image.Point{}, draw.Src)
	return img
}

func setupTestServer(t *testing.T, handler http.HandlerFunc) *httptest.Server {
	server := httptest.NewServer(handler)
	t.Cleanup(func() {
		server.Close()
	})
	return server
}

func createTempDir(t *testing.T) string {
	tmpDir, err := os.MkdirTemp("", "test_*")
	require.NoError(t, err)
	t.Cleanup(func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			t.Fatalf("Failed to remove temp directory: %v", err)
		}
	})
	return tmpDir
}

// Basic functionality tests
func TestSaveAsJPEG(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		img     image.Image
		path    string
		wantErr bool
	}{
		{
			name:    "Valid image and path",
			img:     createTestImage(100, 100),
			path:    filepath.Join(createTempDir(t), "test.jpg"),
			wantErr: false,
		},
		{
			name:    "Nil image",
			img:     nil,
			path:    filepath.Join(createTempDir(t), "test.jpg"),
			wantErr: true,
		},
		{
			name:    "Invalid path",
			img:     createTestImage(100, 100),
			path:    "/invalid/path/test.jpg",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			path, err := saveAsJPEG(tt.img, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)

			// Verify file is valid JPEG
			file, err := os.Open(path)
			require.NoError(t, err)
			defer func() {
				if err := file.Close(); err != nil {
					t.Fatalf("Failed to close file: %v", err)
				}
			}()
			_, err = jpeg.Decode(file)
			assert.NoError(t, err)
		})
	}
}

func TestSaveAsWebP(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		img     image.Image
		path    string
		wantErr bool
	}{
		{
			name:    "Valid image and path",
			img:     createTestImage(100, 100),
			path:    filepath.Join(createTempDir(t), "test.webp"),
			wantErr: false,
		},
		{
			name:    "Nil image",
			img:     nil,
			path:    filepath.Join(createTempDir(t), "test.webp"),
			wantErr: true,
		},
		{
			name:    "Invalid path",
			img:     createTestImage(100, 100),
			path:    "/invalid/path/test.webp",
			wantErr: true,
		},
		{
			name:    "Invalid extension",
			img:     createTestImage(100, 100),
			path:    filepath.Join(createTempDir(t), "test.txt"),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			path, err := saveAsWebP(tt.img, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)

			// Verify WebP file
			file, err := os.Open(path)
			require.NoError(t, err)
			defer func() {
				if err := file.Close(); err != nil {
					t.Fatalf("Failed to close file: %v", err)
				}
			}()
			header := make([]byte, 12)
			_, err = io.ReadFull(file, header)
			require.NoError(t, err)
			assert.Equal(t, "RIFF", string(header[0:4]))
			assert.Equal(t, "WEBP", string(header[8:12]))
		})
	}
}

// Additional saveAsJPEG tests for better coverage
func TestSaveAsJPEG_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		img     image.Image
		path    string
		wantErr bool
		setup   func(t *testing.T, path string)
	}{
		{
			name:    "Very small image",
			img:     createTestImage(1, 1),
			path:    filepath.Join(createTempDir(t), "tiny.jpg"),
			wantErr: false,
		},
		{
			name:    "Large image",
			img:     createTestImage(1000, 1000),
			path:    filepath.Join(createTempDir(t), "large.jpg"),
			wantErr: false,
		},
		{
			name:    "Path with nested directories",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "nested", "deep", "test.jpg"),
			wantErr: false,
		},
		{
			name:    "Path with special characters",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "test-file_123.jpg"),
			wantErr: false,
		},
		{
			name:    "Empty filename",
			img:     createTestImage(50, 50),
			path:    createTempDir(t) + "/",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup(t, tt.path)
			}

			path, err := saveAsJPEG(tt.img, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.NotEmpty(t, path)
			assert.FileExists(t, path)

			// Verify it's a JPEG file
			file, err := os.Open(path)
			require.NoError(t, err)
			defer func() {
				if err := file.Close(); err != nil {
					t.Logf("Failed to close file: %v", err)
				}
			}()
			header := make([]byte, 3)
			_, err = io.ReadFull(file, header)
			require.NoError(t, err)
			assert.Equal(t, []byte{0xFF, 0xD8, 0xFF}, header) // JPEG magic bytes
		})
	}
}

// Additional saveAsWebP tests for better coverage
func TestSaveAsWebP_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		img     image.Image
		path    string
		wantErr bool
		setup   func(t *testing.T, path string)
	}{
		{
			name:    "Empty path",
			img:     createTestImage(50, 50),
			path:    "",
			wantErr: false, // Empty path gets converted to ".webp"
		},
		{
			name:    "Path with no extension",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "test"),
			wantErr: false,
		},
		{
			name:    "Path with .jpg extension (should convert to .webp)",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "test.jpg"),
			wantErr: false,
		},
		{
			name:    "Path with .png extension (should convert to .webp)",
			img:     createTestImage(50, 50),
			path:    filepath.Join(createTempDir(t), "test.png"),
			wantErr: false,
		},
		{
			name:    "Very small image",
			img:     createTestImage(1, 1),
			path:    filepath.Join(createTempDir(t), "tiny.webp"),
			wantErr: false,
		},
		{
			name:    "Large image",
			img:     createTestImage(500, 500),
			path:    filepath.Join(createTempDir(t), "large.webp"),
			wantErr: false,
		},
		{
			name:    "Read-only directory",
			img:     createTestImage(50, 50),
			path:    "/tmp/readonly/test.webp",
			wantErr: true,
			setup: func(t *testing.T, path string) {
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0444); err != nil {
					t.Fatalf("Failed to create read-only directory: %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup(t, tt.path)
			}

			path, err := saveAsWebP(tt.img, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.NotEmpty(t, path)
			assert.FileExists(t, path)

			// Verify it's a WebP file
			if filepath.Ext(path) == ".webp" {
				file, err := os.Open(path)
				require.NoError(t, err)
				defer func() {
					if err := file.Close(); err != nil {
						t.Logf("Failed to close file: %v", err)
					}
				}()
				header := make([]byte, 12)
				_, err = io.ReadFull(file, header)
				require.NoError(t, err)
				assert.Equal(t, "RIFF", string(header[0:4]))
				assert.Equal(t, "WEBP", string(header[8:12]))
			}
		})
	}
}

func TestSaveImage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		img          image.Image
		path         string
		compressWebP bool
		wantErr      bool
		setup        func(t *testing.T, path string)
	}{
		{
			name:         "Save as JPEG",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      false,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "Save as WebP",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      false,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "Nil image",
			img:          nil,
			path:         filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "Invalid path",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "invalid", "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup: func(t *testing.T, path string) {
				// Create a read-only directory
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0755); err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}
				if err := os.Chmod(dir, 0444); err != nil {
					t.Fatalf("Failed to set directory permissions: %v", err)
				}
			},
		},
		{
			name:         "WebP fallback to JPEG",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      false,
			setup:        func(t *testing.T, path string) {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.path)

			path, err := SaveImage(tt.img, tt.path, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)

			// Verify file format
			file, err := os.Open(path)
			require.NoError(t, err)
			defer func() {
				if err := file.Close(); err != nil {
					t.Fatalf("Failed to close file: %v", err)
				}
			}()

			if tt.compressWebP {
				header := make([]byte, 12)
				_, err = io.ReadFull(file, header)
				require.NoError(t, err)
				assert.Equal(t, "RIFF", string(header[0:4]))
				assert.Equal(t, "WEBP", string(header[8:12]))
			} else {
				_, err = jpeg.Decode(file)
				assert.NoError(t, err)
			}
		})
	}
}

// Error handling tests
func TestDownloadWithRetry(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(attempts *int) (int, []byte)
		maxRetries     int
		wantErr        bool
		expectedTries  int
	}{
		{
			name: "Successful first try",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusOK, []byte("test")
			},
			maxRetries:    3,
			wantErr:       false,
			expectedTries: 1,
		},
		{
			name: "Success after two failures",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				if *attempts <= 2 {
					return http.StatusInternalServerError, []byte("error")
				}
				return http.StatusOK, []byte("test")
			},
			maxRetries:    3,
			wantErr:       false,
			expectedTries: 3,
		},
		{
			name: "All attempts fail",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusInternalServerError, []byte("error")
			},
			maxRetries:    2,
			wantErr:       true,
			expectedTries: 2,
		},
		{
			name: "Timeout error",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				time.Sleep(2 * time.Second)
				return http.StatusOK, []byte("test")
			},
			maxRetries:    1,
			wantErr:       true,
			expectedTries: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			attempts := 0
			server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				status, data := tt.serverBehavior(&attempts)
				w.WriteHeader(status)
				if _, err := w.Write(data); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			}))

			// Set a shorter timeout for the test
			client := &http.Client{
				Timeout: 1 * time.Second,
				Transport: &http.Transport{
					ResponseHeaderTimeout: 1 * time.Second,
					ExpectContinueTimeout: 1 * time.Second,
					IdleConnTimeout:       1 * time.Second,
					TLSHandshakeTimeout:   1 * time.Second,
					DialContext: (&net.Dialer{
						Timeout: 1 * time.Second,
					}).DialContext,
					DisableKeepAlives:   true,
					MaxIdleConns:        1,
					MaxConnsPerHost:     1,
					MaxIdleConnsPerHost: 1,
					DisableCompression:  true,
					ForceAttemptHTTP2:   false,
				},
			}
			oldClient := http.DefaultClient
			http.DefaultClient = client
			defer func() {
				http.DefaultClient = oldClient
			}()

			resp, err := downloadWithRetry(server.URL, tt.maxRetries)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, http.StatusOK, resp.StatusCode)
			}
			assert.Equal(t, tt.expectedTries, attempts)
		})
	}
}

// Additional downloadWithRetry tests for better coverage
func TestDownloadWithRetry_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(attempts *int) (int, []byte)
		maxRetries     int
		wantErr        bool
		expectedTries  int
	}{
		{
			name: "Zero retries",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusInternalServerError, []byte("error")
			},
			maxRetries:    0,
			wantErr:       true,
			expectedTries: 0, // With 0 retries, no attempts should be made
		},
		{
			name: "Negative retries",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusInternalServerError, []byte("error")
			},
			maxRetries:    -1,
			wantErr:       true,
			expectedTries: 0, // With negative retries, no attempts should be made
		},
		{
			name: "404 Not Found",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusNotFound, []byte("not found")
			},
			maxRetries:    2,
			wantErr:       true,
			expectedTries: 2,
		},
		{
			name: "403 Forbidden",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				return http.StatusForbidden, []byte("forbidden")
			},
			maxRetries:    2,
			wantErr:       true,
			expectedTries: 2,
		},
		{
			name: "Success on last retry",
			serverBehavior: func(attempts *int) (int, []byte) {
				*attempts++
				if *attempts < 3 {
					return http.StatusBadGateway, []byte("error")
				}
				return http.StatusOK, []byte("success")
			},
			maxRetries:    3,
			wantErr:       false,
			expectedTries: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			attempts := 0
			server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				status, data := tt.serverBehavior(&attempts)
				w.WriteHeader(status)
				if _, err := w.Write(data); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			}))

			resp, err := downloadWithRetry(server.URL, tt.maxRetries)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, http.StatusOK, resp.StatusCode)
			}
			assert.Equal(t, tt.expectedTries, attempts)
		})
	}
}

// Edge cases and special scenarios
func TestDownloadAndSaveImageInDirs(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		savePaths      []string
		compressWebP   bool
		wantErr        bool
		checkResult    func(t *testing.T, results map[string]string)
	}{
		{
			name: "Successful save to multiple paths",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			savePaths: []string{
				filepath.Join(createTempDir(t), "test1.jpg"),
				filepath.Join(createTempDir(t), "test2.jpg"),
			},
			compressWebP: false,
			wantErr:      false,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Len(t, results, 2)
				for _, path := range results {
					assert.FileExists(t, path)
				}
			},
		},
		{
			name: "WebP compression with fallback",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			savePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
			compressWebP: true,
			wantErr:      false,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
					file, err := os.Open(path)
					require.NoError(t, err)
					defer func() {
						if err := file.Close(); err != nil {
							t.Fatalf("Failed to close file: %v", err)
						}
					}()
					header := make([]byte, 12)
					_, err = io.ReadFull(file, header)
					require.NoError(t, err)
					assert.Equal(t, "RIFF", string(header[0:4]))
					assert.Equal(t, "WEBP", string(header[8:12]))
				}
			},
		},
		{
			name: "Invalid image data",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			savePaths:    []string{filepath.Join(createTempDir(t), "test.jpg")},
			compressWebP: false,
			wantErr:      true,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Partial success",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			savePaths: []string{
				filepath.Join(createTempDir(t), "valid.jpg"),
				"/invalid/path/test.jpg",
			},
			compressWebP: false,
			wantErr:      false,
			checkResult: func(t *testing.T, results map[string]string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			results, err := DownloadAndSaveImageInDirs(server.URL, tt.savePaths, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
				return
			}
			assert.NoError(t, err)
			tt.checkResult(t, results)
		})
	}
}

func TestDownloadAndResizeImage(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		width          int
		height         int
		wantErr        bool
		checkSize      func(t *testing.T, img image.Image)
	}{
		{
			name: "Successful resize landscape",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(200, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			width:   100,
			height:  100,
			wantErr: false,
			checkSize: func(t *testing.T, img image.Image) {
				bounds := img.Bounds()
				assert.Equal(t, 100, bounds.Dx())
				assert.Equal(t, 50, bounds.Dy())
			},
		},
		{
			name: "Successful resize portrait",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 200)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			width:   100,
			height:  100,
			wantErr: false,
			checkSize: func(t *testing.T, img image.Image) {
				bounds := img.Bounds()
				assert.Equal(t, 50, bounds.Dx())
				assert.Equal(t, 100, bounds.Dy())
			},
		},
		{
			name: "Invalid image data",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			width:   100,
			height:  100,
			wantErr: true,
		},
		{
			name: "Server error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			width:   100,
			height:  100,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			img, err := DownloadAndResizeImage(server.URL, tt.width, tt.height)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, img)
				return
			}
			assert.NoError(t, err)
			assert.NotNil(t, img)
			tt.checkSize(t, img)
		})
	}
}

// Add more test cases to improve coverage
func TestDownloadAndSaveImageInDirs_WithMultipleFormats(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)
		img := createTestImage(100, 100)
		if err := jpeg.Encode(w, img, nil); err != nil {
			t.Fatalf("Failed to encode image: %v", err)
		}
	}))

	savePaths := []string{
		filepath.Join(createTempDir(t), "test1.webp"),
		filepath.Join(createTempDir(t), "test2.jpg"),
	}

	results, err := DownloadAndSaveImageInDirs(server.URL, savePaths, true)
	assert.NoError(t, err)
	assert.Len(t, results, 2)

	for _, path := range results {
		assert.FileExists(t, path)
		file, err := os.Open(path)
		require.NoError(t, err)
		defer func() {
			if err := file.Close(); err != nil {
				t.Fatalf("Failed to close file: %v", err)
			}
		}()

		if filepath.Ext(path) == ".webp" {
			header := make([]byte, 12)
			_, err = io.ReadFull(file, header)
			require.NoError(t, err)
			assert.Equal(t, "RIFF", string(header[0:4]))
			assert.Equal(t, "WEBP", string(header[8:12]))
		} else {
			_, err = jpeg.Decode(file)
			assert.NoError(t, err)
		}
	}
}

// TestSaveImage_WebPFallbackExtension tests that when WebP saving fails and falls back to JPEG,
// the file extension is correctly changed from .webp to .jpg
func TestSaveImage_WebPFallbackExtension(t *testing.T) {
	setupTestProcess(t)

	// Create a test image
	img := createTestImage(100, 100)
	tempDir := createTempDir(t)

	// Test with .webp extension
	webpPath := filepath.Join(tempDir, "test.webp")

	// Save with WebP compression (may fallback to JPEG depending on system)
	savedPath, err := SaveImage(img, webpPath, true)
	assert.NoError(t, err)
	assert.NotEmpty(t, savedPath)

	// Check that the file exists
	assert.FileExists(t, savedPath)

	// If WebP encoding failed and fell back to JPEG, the extension should be .jpg
	if filepath.Ext(savedPath) == ".jpg" {
		// Verify the original .webp file doesn't exist
		assert.NoFileExists(t, webpPath)

		// Verify the .jpg file exists and has correct extension
		assert.True(t, strings.HasSuffix(savedPath, ".jpg"))

		// Verify the base name is correct (should be "test.jpg")
		expectedJpgPath := filepath.Join(tempDir, "test.jpg")
		assert.Equal(t, expectedJpgPath, savedPath)

		t.Log("WebP fallback correctly changed extension from .webp to .jpg")
	} else {
		// WebP encoding succeeded, file should have .webp extension
		assert.True(t, strings.HasSuffix(savedPath, ".webp"))
		assert.Equal(t, webpPath, savedPath)

		t.Log("WebP encoding succeeded, extension remained .webp")
	}
}

// TestSaveImage_ForceWebPFallback tests the extension change when WebP fails
func TestSaveImage_ForceWebPFallback(t *testing.T) {
	setupTestProcess(t)

	// Create a test image
	img := createTestImage(100, 100)
	tempDir := createTempDir(t)

	// Create a directory that will cause WebP save to fail
	webpPath := filepath.Join(tempDir, "readonly", "test.webp")

	// Create the directory structure
	dir := filepath.Dir(webpPath)
	err := os.MkdirAll(dir, 0755)
	assert.NoError(t, err)

	// Make directory read-only to force WebP save failure
	err = os.Chmod(dir, 0444)
	assert.NoError(t, err)

	// Restore permissions after test
	defer func() {
		if err := os.Chmod(dir, 0755); err != nil {
			t.Logf("Failed to restore directory permissions: %v", err)
		}
		if err := os.RemoveAll(tempDir); err != nil {
			t.Logf("Failed to remove temp directory: %v", err)
		}
	}()

	// Try to save with WebP compression - should fail and fallback to JPEG
	savedPath, err := SaveImage(img, webpPath, true)

	// The save should fail because we can't write to read-only directory
	// This tests that our error handling works correctly
	assert.Error(t, err)
	assert.Empty(t, savedPath)
}

// TestSaveImage_ExtensionChange tests extension change logic directly
func TestSaveImage_ExtensionChange(t *testing.T) {
	setupTestProcess(t)

	// Test the extension change logic by examining the path transformation
	testCases := []struct {
		name         string
		originalPath string
		expectedJpg  string
	}{
		{
			name:         "webp to jpg",
			originalPath: "/path/to/image.webp",
			expectedJpg:  "/path/to/image.jpg",
		},
		{
			name:         "WEBP to jpg",
			originalPath: "/path/to/image.WEBP",
			expectedJpg:  "/path/to/image.jpg",
		},
		{
			name:         "no extension",
			originalPath: "/path/to/image",
			expectedJpg:  "/path/to/image.jpg",
		},
		{
			name:         "multiple dots",
			originalPath: "/path/to/image.test.webp",
			expectedJpg:  "/path/to/image.test.jpg",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Simulate the extension change logic from SaveImage
			ext := filepath.Ext(tc.originalPath)
			jpgPath := tc.originalPath[:len(tc.originalPath)-len(ext)] + ".jpg"
			assert.Equal(t, tc.expectedJpg, jpgPath)
		})
	}
}

func TestDownloadAndSaveImageInDirs_WithEmptyPaths(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)
		img := createTestImage(100, 100)
		if err := jpeg.Encode(w, img, nil); err != nil {
			t.Fatalf("Failed to encode image: %v", err)
		}
	}))

	results, err := DownloadAndSaveImageInDirs(server.URL, []string{}, true)
	assert.Error(t, err)
	assert.Empty(t, results)
}

func TestDownloadAndResizeImage_WithInvalidDimensions(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)
		img := createTestImage(100, 100)
		if err := jpeg.Encode(w, img, nil); err != nil {
			t.Fatalf("Failed to encode image: %v", err)
		}
	}))

	// Test with negative dimensions
	img, err := DownloadAndResizeImage(server.URL, -1, -1)
	assert.Error(t, err)
	assert.Nil(t, img)

	// Test with zero dimensions
	img, err = DownloadAndResizeImage(server.URL, 0, 0)
	assert.Error(t, err)
	assert.Nil(t, img)

	// Test with one zero dimension
	img, err = DownloadAndResizeImage(server.URL, 100, 0)
	assert.Error(t, err)
	assert.Nil(t, img)

	// Test with one negative dimension
	img, err = DownloadAndResizeImage(server.URL, 100, -1)
	assert.Error(t, err)
	assert.Nil(t, img)
}

func TestDownloadAndSaveFile(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
		checkResult    func(t *testing.T, results []string)
	}{
		{
			name: "Successful PDF download",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				// Write a minimal valid PDF file
				if _, err := w.Write([]byte("%PDF-1.4\n%\xe2\xe3\xcf\xd3\n1 0 obj\n<<>>\nendobj\ntrailer\n<<>>\n%%EOF")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.pdf",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.pdf")},
				IsPhoto:   false,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
					// Read first few bytes to verify PDF header
					data, err := os.ReadFile(path)
					require.NoError(t, err)
					assert.True(t, bytes.HasPrefix(data, []byte("%PDF-")))
				}
			},
		},
		{
			name: "Successful photo download with WebP compression",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(100, 100)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:          "http://example.com/test.jpg",
				SavePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
				IsPhoto:      true,
				CompressWebP: true,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 1)
				for _, path := range results {
					assert.FileExists(t, path)
					file, err := os.Open(path)
					require.NoError(t, err)
					defer func() {
						if err := file.Close(); err != nil {
							t.Fatalf("Failed to close file: %v", err)
						}
					}()
					header := make([]byte, 12)
					_, err = io.ReadFull(file, header)
					require.NoError(t, err)
					assert.Equal(t, "RIFF", string(header[0:4]))
					assert.Equal(t, "WEBP", string(header[8:12]))
				}
			},
		},
		{
			name: "Multiple save paths",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("%PDF-1.4")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL: "http://example.com/test.pdf",
				SavePaths: []string{
					filepath.Join(createTempDir(t), "test1.pdf"),
					filepath.Join(createTempDir(t), "test2.pdf"),
				},
				IsPhoto: false,
			},
			wantErr: false,
			checkResult: func(t *testing.T, results []string) {
				assert.Len(t, results, 2)
				for _, path := range results {
					assert.FileExists(t, path)
				}
			},
		},
		{
			name: "Server error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/error.pdf",
				SavePaths: []string{filepath.Join(createTempDir(t), "error.pdf")},
				IsPhoto:   false,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Invalid save path",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("%PDF-1.4")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.pdf",
				SavePaths: []string{"/invalid/path/test.pdf"},
				IsPhoto:   false,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Timeout error",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				time.Sleep(2 * time.Second)
				w.Header().Set("Content-Type", "application/pdf")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("%PDF-1.4")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:        "http://example.com/timeout.pdf",
				SavePaths:  []string{filepath.Join(createTempDir(t), "timeout.pdf")},
				IsPhoto:    false,
				MaxRetries: 1,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Nil options",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
			},
			opts:    nil,
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Empty save paths",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.pdf",
				SavePaths: []string{},
				IsPhoto:   false,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL

				// Set a shorter timeout for timeout test
				if tt.name == "Timeout error" {
					client := &http.Client{
						Timeout: 1 * time.Second,
						Transport: &http.Transport{
							ResponseHeaderTimeout: 1 * time.Second,
							ExpectContinueTimeout: 1 * time.Second,
							IdleConnTimeout:       1 * time.Second,
							TLSHandshakeTimeout:   1 * time.Second,
							DialContext: (&net.Dialer{
								Timeout: 1 * time.Second,
							}).DialContext,
							DisableKeepAlives:   true,
							MaxIdleConns:        1,
							MaxConnsPerHost:     1,
							MaxIdleConnsPerHost: 1,
							DisableCompression:  true,
							ForceAttemptHTTP2:   false,
						},
					}
					oldClient := http.DefaultClient
					http.DefaultClient = client
					defer func() {
						http.DefaultClient = oldClient
					}()
				}
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
			tt.checkResult(t, results)
		})
	}
}

func TestSaveNotPic(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		data    []byte
		path    string
		wantErr bool
		setup   func(t *testing.T, path string)
	}{
		{
			name:    "Valid file and path",
			data:    []byte("test data"),
			path:    filepath.Join(createTempDir(t), "test.txt"),
			wantErr: false,
			setup:   func(t *testing.T, path string) {},
		},
		{
			name:    "Invalid path",
			data:    []byte("test data"),
			path:    "/invalid/path/test.txt",
			wantErr: true,
			setup:   func(t *testing.T, path string) {},
		},
		{
			name:    "Read-only directory",
			data:    []byte("test data"),
			path:    filepath.Join(createTempDir(t), "test", "test.txt"),
			wantErr: true,
			setup: func(t *testing.T, path string) {
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0755); err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}
				if err := os.Chmod(dir, 0444); err != nil {
					t.Fatalf("Failed to set directory permissions: %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.path)
			path, err := saveNotPic(tt.data, tt.path)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)
			data, err := os.ReadFile(path)
			require.NoError(t, err)
			assert.Equal(t, tt.data, data)
		})
	}
}

func TestDownloadAndSaveFile_AdditionalCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
		checkResult    func(t *testing.T, results []string)
	}{
		{
			name: "Invalid image data with WebP compression",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:          "http://example.com/test.jpg",
				SavePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
				IsPhoto:      true,
				CompressWebP: true,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Server timeout",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				time.Sleep(2 * time.Second)
				w.WriteHeader(http.StatusOK)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:        "http://example.com/timeout.jpg",
				SavePaths:  []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:    true,
				MaxRetries: 1,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Server error with retry",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:        "http://example.com/error.jpg",
				SavePaths:  []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:    true,
				MaxRetries: 2,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
			tt.checkResult(t, results)
		})
	}
}

func TestGetImageDir(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name     string
		board    string
		wantDirs []string
	}{
		{
			name:     "Valid board",
			board:    "TRB",
			wantDirs: []string{"/tmp/trb_images", "/tmp/trb_images2"},
		},
		{
			name:     "Invalid board",
			board:    "INVALID",
			wantDirs: nil,
		},
		{
			name:     "Empty board",
			board:    "",
			wantDirs: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dirs := levelStore.GetImageDir(tt.board)
			assert.Equal(t, tt.wantDirs, dirs)
		})
	}
}

func TestBase62Conversion_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name     string
		input    int32
		expected string
		wantErr  bool
	}{
		{
			name:     "Max int32",
			input:    2147483647,
			expected: "CVUmlB",
			wantErr:  false,
		},
		{
			name:     "Min int32",
			input:    -2147483648,
			expected: "CVUmlC",
			wantErr:  false,
		},
		{
			name:     "Single digit",
			input:    1,
			expected: "B",
			wantErr:  false,
		},
		{
			name:     "Two digits",
			input:    62,
			expected: "BA",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test Int32ToBase62
			result, err := levelStore.Int32ToBase62(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)

			// Test Base62ToInt32
			num, err := levelStore.Base62ToInt32(result)
			assert.NoError(t, err)
			assert.Equal(t, tt.input, num)
		})
	}
}

func TestDownloadAndSaveFile_MoreErrorCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name           string
		serverBehavior func(w http.ResponseWriter, r *http.Request)
		opts           *DownloadAndSaveFileOptions
		wantErr        bool
		checkResult    func(t *testing.T, results []string)
	}{
		{
			name: "Empty response body",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.jpg",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:   true,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Invalid image data with JPEG",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.jpg",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:   true,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
		{
			name: "Invalid image data with WebP",
			serverBehavior: func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/webp")
				w.WriteHeader(http.StatusOK)
				if _, err := w.Write([]byte("invalid image data")); err != nil {
					t.Fatalf("Failed to write data: %v", err)
				}
			},
			opts: &DownloadAndSaveFileOptions{
				URL:          "http://example.com/test.webp",
				SavePaths:    []string{filepath.Join(createTempDir(t), "test.webp")},
				IsPhoto:      true,
				CompressWebP: true,
			},
			wantErr: true,
			checkResult: func(t *testing.T, results []string) {
				assert.Empty(t, results)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server := setupTestServer(t, http.HandlerFunc(tt.serverBehavior))
			if tt.opts != nil {
				tt.opts.URL = server.URL
			}
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, results)
			}
			tt.checkResult(t, results)
		})
	}
}

func TestSaveImage_EdgeCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		img          image.Image
		path         string
		compressWebP bool
		wantErr      bool
		setup        func(t *testing.T, path string)
	}{
		{
			name:         "Empty path",
			img:          createTestImage(100, 100),
			path:         "",
			compressWebP: false,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "Read-only directory",
			img:          createTestImage(100, 100),
			path:         filepath.Join(createTempDir(t), "readonly", "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup: func(t *testing.T, path string) {
				dir := filepath.Dir(path)
				if err := os.MkdirAll(dir, 0755); err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}
				if err := os.Chmod(dir, 0444); err != nil {
					t.Fatalf("Failed to set directory permissions: %v", err)
				}
			},
		},
		{
			name:         "WebP compression with invalid image",
			img:          nil,
			path:         filepath.Join(createTempDir(t), "test.webp"),
			compressWebP: true,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
		},
		{
			name:         "JPEG with invalid image",
			img:          nil,
			path:         filepath.Join(createTempDir(t), "test.jpg"),
			compressWebP: false,
			wantErr:      true,
			setup:        func(t *testing.T, path string) {},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t, tt.path)
			path, err := SaveImage(tt.img, tt.path, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, path)
				return
			}
			assert.NoError(t, err)
			assert.FileExists(t, path)
		})
	}
}

// Test downloadWithRetry with DefaultClient timeout
func TestDownloadWithRetry_DefaultClientTimeout(t *testing.T) {
	setupTestProcess(t)

	// Save original client
	originalClient := http.DefaultClient
	defer func() {
		http.DefaultClient = originalClient
	}()

	// Set DefaultClient with timeout
	http.DefaultClient = &http.Client{
		Timeout: 5 * time.Second,
	}

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte("test")); err != nil {
			t.Fatalf("Failed to write data: %v", err)
		}
	}))

	resp, err := downloadWithRetry(server.URL, 1)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, http.StatusOK, resp.StatusCode)
}

// Test downloadWithRetry with network errors
func TestDownloadWithRetry_NetworkErrors(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		url     string
		wantErr bool
	}{
		{
			name:    "Invalid URL scheme",
			url:     "ftp://invalid-scheme.com",
			wantErr: true,
		},
		{
			name:    "Malformed URL",
			url:     "http://[::1:invalid",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := downloadWithRetry(tt.url, 1)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
			}
		})
	}
}

// Additional tests for better coverage
func TestDownloadAndSaveFile_NetworkErrors(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name    string
		url     string
		opts    *DownloadAndSaveFileOptions
		wantErr bool
	}{
		{
			name: "Invalid URL",
			url:  "invalid-url",
			opts: &DownloadAndSaveFileOptions{
				URL:       "invalid-url",
				SavePaths: []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:   true,
			},
			wantErr: true,
		},
		{
			name: "Non-existent host",
			url:  "http://non-existent-host-12345.com/test.jpg",
			opts: &DownloadAndSaveFileOptions{
				URL:        "http://non-existent-host-12345.com/test.jpg",
				SavePaths:  []string{filepath.Join(createTempDir(t), "test.jpg")},
				IsPhoto:    true,
				MaxRetries: 1,
			},
			wantErr: true,
		},
		{
			name: "Empty save paths",
			url:  "http://example.com/test.jpg",
			opts: &DownloadAndSaveFileOptions{
				URL:       "http://example.com/test.jpg",
				SavePaths: []string{},
				IsPhoto:   true,
			},
			wantErr: true, // Should fail because there are no paths to save to
		},
		{
			name:    "Nil options",
			url:     "http://example.com/test.jpg",
			opts:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := DownloadAndSaveFile(tt.opts)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, results)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test for DownloadAndSaveImageInDirs edge cases
func TestDownloadAndSaveImageInDirs_MoreCases(t *testing.T) {
	setupTestProcess(t)

	tests := []struct {
		name         string
		url          string
		savePaths    []string
		compressWebP bool
		wantErr      bool
	}{
		{
			name:         "Empty save paths",
			url:          "http://example.com/test.jpg",
			savePaths:    []string{},
			compressWebP: false,
			wantErr:      true, // Should fail because there are no paths to save to
		},
		{
			name:         "Nil save paths",
			url:          "http://example.com/test.jpg",
			savePaths:    nil,
			compressWebP: false,
			wantErr:      true, // Should fail because there are no paths to save to
		},
		{
			name: "Mixed valid and invalid paths",
			url:  "http://example.com/test.jpg",
			savePaths: []string{
				filepath.Join(createTempDir(t), "valid.jpg"),
				"/invalid/path/test.jpg",
				filepath.Join(createTempDir(t), "valid2.jpg"),
			},
			compressWebP: false,
			wantErr:      false, // Should partially succeed
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test server that serves a valid image
			server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "image/jpeg")
				w.WriteHeader(http.StatusOK)
				img := createTestImage(50, 50)
				if err := jpeg.Encode(w, img, nil); err != nil {
					t.Fatalf("Failed to encode image: %v", err)
				}
			}))

			_, err := DownloadAndSaveImageInDirs(server.URL, tt.savePaths, tt.compressWebP)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test WebP fallback to JPEG
func TestDownloadAndSaveFile_WebPFallback(t *testing.T) {
	setupTestProcess(t)

	// Create a test server that serves an image
	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "image/jpeg")
		w.WriteHeader(http.StatusOK)
		img := createTestImage(100, 100)
		if err := jpeg.Encode(w, img, nil); err != nil {
			t.Fatalf("Failed to encode image: %v", err)
		}
	}))

	// Test with WebP compression to an invalid path (should fallback to JPEG)
	tempDir := createTempDir(t)
	webpPath := filepath.Join(tempDir, "test.webp")

	// Make the directory read-only to force WebP save to fail
	if err := os.Chmod(tempDir, 0444); err != nil {
		t.Fatalf("Failed to make directory read-only: %v", err)
	}
	defer func() {
		// Restore permissions for cleanup
		if err := os.Chmod(tempDir, 0755); err != nil {
			t.Logf("Failed to restore directory permissions: %v", err)
		}
	}()

	opts := &DownloadAndSaveFileOptions{
		URL:          server.URL,
		SavePaths:    []string{webpPath},
		IsPhoto:      true,
		CompressWebP: true,
		MaxRetries:   1,
	}

	results, err := DownloadAndSaveFile(opts)
	// Should fail because both WebP and JPEG fallback will fail due to read-only directory
	assert.Error(t, err)
	assert.Empty(t, results)
}

// Test DownloadAndSaveFile with empty data
func TestDownloadAndSaveFile_EmptyData(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/pdf")
		w.WriteHeader(http.StatusOK)
		// Don't write any data
	}))

	opts := &DownloadAndSaveFileOptions{
		URL:       server.URL,
		SavePaths: []string{filepath.Join(createTempDir(t), "empty.pdf")},
		IsPhoto:   false,
	}

	results, err := DownloadAndSaveFile(opts)
	assert.NoError(t, err)
	assert.Len(t, results, 1)

	// Verify the file exists but is empty
	data, err := os.ReadFile(results[0])
	assert.NoError(t, err)
	assert.Empty(t, data)
}

// Test DownloadAndSaveFile with MaxRetries = 0 (should use default)
func TestDownloadAndSaveFile_DefaultRetries(t *testing.T) {
	setupTestProcess(t)

	server := setupTestServer(t, http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/plain")
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte("test content")); err != nil {
			t.Fatalf("Failed to write data: %v", err)
		}
	}))

	opts := &DownloadAndSaveFileOptions{
		URL:        server.URL,
		SavePaths:  []string{filepath.Join(createTempDir(t), "test.txt")},
		IsPhoto:    false,
		MaxRetries: 0, // Should use default
	}

	results, err := DownloadAndSaveFile(opts)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.FileExists(t, results[0])
}
