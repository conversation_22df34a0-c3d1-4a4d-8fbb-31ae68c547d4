# Memory Leak Fixes for GoResoDownload Service

## Problem
The `goresodownloadTRB` service was experiencing Out of Memory (OOM) kills after running for extended periods, consuming 30+ minutes of CPU time before being killed by the system.

## Root Causes Identified

### 1. Unbounded Goroutine Creation
- The original `ProcessQueue` function created unlimited goroutines for each batch item
- No concurrency limits led to memory explosion under high load
- Goroutines could accumulate without proper cleanup

### 2. Missing Context Cancellation
- Download operations had no timeout or cancellation mechanism
- Long-running downloads could block indefinitely
- No graceful shutdown handling

### 3. Memory Monitoring Absence
- No proactive memory usage monitoring
- No garbage collection triggers
- No early warning system for memory issues

### 4. Resource Cleanup Issues
- Missing cleanup during service shutdown
- Goroutines could leak during context cancellation
- No proper resource deallocation

## Solutions Implemented

### 1. Concurrency Control
```go
// Added semaphore to limit concurrent processing
semaphore := make(chan struct{}, 5) // Limit to 5 concurrent operations
```

### 2. Context-Aware Operations
```go
// Added timeout contexts for all operations
ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
defer cancel()
```

### 3. Memory Monitoring
```go
// Added proactive memory monitoring
func monitorMemoryUsage(ctx context.Context) {
    // Monitors memory every 30 seconds
    // Triggers GC when usage exceeds 500MB or 50% of system memory
    // Logs warnings with detailed memory stats
}
```

### 4. Graceful Shutdown
```go
// Added proper cleanup during shutdown
func main() {
    // ... service logic ...
    <-ctx.Done()
    
    // Clean up resources
    queueCancel()
    watchCancel()
    cancelCtx()
    
    // Wait for goroutines to finish
    time.Sleep(5 * time.Second)
    
    // Force garbage collection
    runtime.GC()
}
```

### 5. Systemd Service Configuration
Created `goresodownloadTRB.service` with:
- Memory limits: `MemoryMax=1G`, `MemoryLimit=800M`
- CPU limits: `CPUQuota=200%`
- Process limits: `LimitNOFILE=65536`
- Environment variables: `GOMAXPROCS=4`, `GOGC=50`

## Configuration Changes

### Downloader Configuration
```go
type DownloaderConfig struct {
    DownloadConcurrency: 3,        // Reduced from 10
    DeleteConcurrency:   3,        // Reduced from 10
    MaxRetries:          3,
    MaxMemoryMB:         500,      // New: memory limit
    BatchSize:           10,       // New: batch size limit
}
```

### Process Queue Improvements
- Added semaphore-based concurrency control
- Added timeout contexts for each operation
- Added memory monitoring goroutine
- Improved error handling and cleanup

## Monitoring and Alerts

### Memory Usage Logging
The service now logs memory usage every 30 seconds:
```
level=DEBUG msg=Memory usage normal currentMemory=150.2 MB heapAlloc=120.1 MB numGoroutines=25
```

### High Memory Warnings
When memory usage exceeds limits:
```
level=WARN msg=High memory usage detected currentMemory=600.5 MB percentage=75.2% numGoroutines=150
```

### Goroutine Count Monitoring
The service tracks active goroutines and logs warnings when the count is high.

## Deployment Instructions

### 1. Update Service Configuration
```bash
# Copy the new service file
sudo cp goresodownloadTRB.service /etc/systemd/system/

# Reload systemd
sudo systemctl daemon-reload

# Restart the service
sudo systemctl restart goresodownloadTRB
```

### 2. Monitor Service Status
```bash
# Check service status
sudo systemctl status goresodownloadTRB

# View logs
sudo journalctl -u goresodownloadTRB -f

# Check memory usage
sudo systemctl show goresodownloadTRB --property=MemoryCurrent,MemoryMax
```

### 3. Verify Memory Limits
```bash
# Check if memory limits are applied
cat /proc/$(pgrep goresodownload)/cgroup | grep memory
```

## Expected Results

### Before Fixes
- Service would run for 30+ minutes before OOM kill
- Unbounded memory growth
- No visibility into memory usage
- Sudden service termination

### After Fixes
- Memory usage capped at 1GB
- Proactive memory monitoring and GC
- Graceful degradation under load
- Detailed memory usage logging
- Controlled concurrency prevents resource exhaustion

## Monitoring Commands

### Check Memory Usage
```bash
# Real-time memory monitoring
watch -n 1 'ps aux | grep goresodownload | grep -v grep'

# Systemd memory stats
sudo systemctl show goresodownloadTRB --property=MemoryCurrent,MemoryMax,MemoryLimit
```

### Check Goroutine Count
```bash
# Monitor goroutines (if pprof is enabled)
curl http://localhost:6060/debug/pprof/goroutine?debug=1
```

### Check Service Health
```bash
# Service status
sudo systemctl is-active goresodownloadTRB

# Recent logs
sudo journalctl -u goresodownloadTRB --since "10 minutes ago"
```

## Troubleshooting

### If Service Still OOMs
1. Check if memory limits are properly applied
2. Verify systemd configuration is loaded
3. Check for memory leaks in external dependencies
4. Consider reducing concurrency limits further

### If Performance Degrades
1. Monitor CPU usage and adjust `CPUQuota`
2. Check if `GOMAXPROCS` is appropriate for your system
3. Adjust `GOGC` for more aggressive garbage collection

### If Service Won't Start
1. Check systemd logs: `sudo journalctl -u goresodownloadTRB`
2. Verify file permissions and paths
3. Check if required directories exist
4. Ensure MongoDB is accessible 