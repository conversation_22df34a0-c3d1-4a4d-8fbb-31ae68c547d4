[Unit]
Description=GoResoDownload TRB Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/home/<USER>/github/go
ExecStart=/home/<USER>/github/go/start.sh -n goresodownloadTRB -d "goresodownload" -cmd "cmd/goresodownload/main.go -board TRB -force"
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Memory limits to prevent OOM
MemoryMax=1G
MemoryLimit=800M
MemoryAccounting=yes

# CPU limits
CPUQuota=200%
CPUAccounting=yes

# Process limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/github/go

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=goresodownloadTRB

# Environment variables
Environment=GOMAXPROCS=4
Environment=GOGC=50

[Install]
WantedBy=multi-user.target 