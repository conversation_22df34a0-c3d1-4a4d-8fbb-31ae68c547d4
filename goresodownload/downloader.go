package goresodownload

import (
	"context"
	"fmt"
	"image/jpeg"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/real-rm/gofile"
	"github.com/real-rm/gofile/levelStore"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	THUMBNAIL_WIDTH  = 240
	THUMBNAIL_HEIGHT = 160
)

// FailedTask represents a failed download task in the database
type FailedTask struct {
	ID         primitive.ObjectID `bson:"_id,omitempty"`
	PropID     string             `bson:"prop_id"`
	ImageURL   string             `bson:"image_url"`
	ErrMsg     string             `bson:"err_msg"`
	RetryCount int                `bson:"retry_count"`
}

// DownloaderConfig represents the configuration for the downloader
type DownloaderConfig struct {
	// Concurrency settings
	DownloadConcurrency int `json:"download_concurrency"`
	DeleteConcurrency   int `json:"delete_concurrency"`

	// Retry settings
	MaxRetries int `json:"max_retries"`

	// Alert settings
	ConsecutiveFailuresThreshold int `json:"consecutive_failures_threshold"` // Default: 3
}

// NewDefaultConfig returns a new DownloaderConfig with default values
func NewDefaultConfig() *DownloaderConfig {
	return &DownloaderConfig{
		DownloadConcurrency:          1, // Restored to 3 for better performance
		DeleteConcurrency:            3, // Restored to 3 for better performance
		MaxRetries:                   2, // Reduced from 3 to 2 to prevent failure accumulation
		ConsecutiveFailuresThreshold: 3,
	}
}

// DownloaderOptions contains all options for creating a Downloader
type DownloaderOptions struct {
	Config       *DownloaderConfig
	StoragePaths []string
	MergedCol    *gomongo.MongoCollection
	FailedCol    *gomongo.MongoCollection
}

// Downloader implements the media download functionality
type Downloader struct {
	*DownloaderOptions
}

// NewDownloader creates a new Downloader instance with the given options
func NewDownloader(opts *DownloaderOptions) (*Downloader, error) {
	if opts == nil {
		opts = &DownloaderOptions{
			Config: NewDefaultConfig(),
		}
	}
	if opts.Config == nil {
		opts.Config = NewDefaultConfig()
	}

	return &Downloader{
		DownloaderOptions: opts,
	}, nil
}

// ProcessAnalysisResult processes the analysis result and performs downloads/deletions
func (d *Downloader) ProcessAnalysisResult(result *AnalysisResult) (tnChangedNum int, err error) {
	var wg sync.WaitGroup
	errChan := make(chan error, 2) // Buffer size 2 for both download and deletion errors

	// Process downloads
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := d.processDownloads(result.DownloadTasks, result.ID); err != nil {
			errChan <- fmt.Errorf("download processing failed: %w", err)
		}
	}()

	// Process deletions
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := d.processDeletions(result.DeleteTasks); err != nil {
			errChan <- fmt.Errorf("deletion processing failed: %w", err)
		}
	}()

	// Wait for both operations to complete
	wg.Wait()
	close(errChan)

	// Check for errors
	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}
	if len(errs) > 0 {
		return 0, fmt.Errorf("processing failed: %v", errs)
	}

	// Generate thumbnail and update document
	var thumbnailHash int32
	if result.NewFirstPic.MediaKey != "" {
		var err error
		thumbnailHash, tnChangedNum, err = d.generateThumbnailAndUpdate(result.NewFirstPic, result.OldTnLH)
		if err != nil {
			golog.Error("failed to generate thumbnail",
				"error", err,
				"mediaKey", result.NewFirstPic.MediaKey,
				"url", result.NewFirstPic.URL,
				"oldTnLH", result.OldTnLH)
		}
	}

	// Update document with all fields
	golog.Debug("ProcessAnalysisResult", "result", result, "thumbnailHash", thumbnailHash)
	if err := d.updateMergedDoc(result, thumbnailHash); err != nil {
		golog.Error("failed to update merged document", "error", err)
	}
	golog.Info("ProcessAnalysisResult finished", "ID", result.ID)
	return tnChangedNum, nil
}

// updateMergedDoc updates the merged document with PhoLH, DocLH and optional thumbnail hash
func (d *Downloader) updateMergedDoc(result *AnalysisResult, thumbnailHash int32) error {
	if result == nil {
		golog.Error("updateMergedDoc", "result", result)
		return fmt.Errorf("result is nil")
	}

	// Check if document exists
	var doc bson.M
	err := d.MergedCol.FindOne(context.Background(), bson.M{"_id": result.ID}).Decode(&doc)
	if err != nil {
		golog.Error("updateMergedDoc", "error", err)
		return fmt.Errorf("document not found: %w", err)
	}

	update := bson.M{}
	update["$set"] = bson.M{}
	if len(result.PhoLH) == 0 && len(result.DocLH) == 0 {
		return nil
	}
	golog.Debug("updateMergedDoc", "result", result.DocLH, "thumbnailHash", thumbnailHash)
	if len(result.PhoLH) > 0 {
		update["$set"].(bson.M)["phoLH"] = result.PhoLH
	}
	if len(result.DocLH) > 0 {
		update["$set"].(bson.M)["docLH"] = result.DocLH
	}

	// Add thumbnail hash if available
	if thumbnailHash != 0 {
		update["$set"].(bson.M)["tnLH"] = thumbnailHash
	}

	golog.Debug("updateMergedDoc", "update", update)

	_, err = d.MergedCol.UpdateOne(
		context.Background(),
		bson.M{"_id": result.ID},
		update,
	)
	return err
}

// processDownloads handles the download tasks with concurrency
func (d *Downloader) processDownloads(tasks []MediaTask, id string) error {
	if len(tasks) == 0 {
		golog.Info("no tasks to download", "id", id)
		return nil
	}

	sem := make(chan struct{}, d.Config.DownloadConcurrency)
	errChan := make(chan error, len(tasks))
	var wg sync.WaitGroup

	for _, task := range tasks {
		wg.Add(1)
		go func(task MediaTask) {
			defer wg.Done()
			sem <- struct{}{}        // Acquire semaphore
			defer func() { <-sem }() // Release semaphore

			if err := d.downloadTask(task, id); err != nil {
				errChan <- err
			}
		}(task)
	}

	wg.Wait()
	close(errChan)

	// Check for errors
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}

// processDeletions handles the deletion tasks with concurrency
func (d *Downloader) processDeletions(tasks []DeleteTask) error {
	if len(tasks) == 0 {
		golog.Debug("no tasks to delete")
		return nil
	}

	sem := make(chan struct{}, d.Config.DeleteConcurrency)
	errChan := make(chan error, len(tasks))
	var wg sync.WaitGroup

	for _, task := range tasks {
		wg.Add(1)
		go func(task DeleteTask) {
			defer wg.Done()
			sem <- struct{}{}        // Acquire semaphore
			defer func() { <-sem }() // Release semaphore

			if err := d.deleteTask(task); err != nil {
				errChan <- err
			}
		}(task)
	}

	wg.Wait()
	close(errChan)

	// Check for errors
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}

// downloadTask handles a single download task
func (d *Downloader) downloadTask(task MediaTask, id string) error {
	var lastErr error
	consecutiveFailures := 0

	for i := range make([]struct{}, d.Config.MaxRetries) {
		// Prepare full paths for all storage locations
		var fullPaths []string
		for _, basePath := range d.StoragePaths {
			fullPath := filepath.Join(basePath, task.DestPath)
			fullPaths = append(fullPaths, fullPath)
		}

		// Ensure URL has proper protocol prefix
		url := task.URL
		if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
			url = "https://" + url
		}

		golog.Debug("downloading file", "url", url, "savePaths", fullPaths)
		opts := &gofile.DownloadAndSaveFileOptions{
			URL:          url,
			SavePaths:    fullPaths,
			CompressWebP: false,
			IsPhoto:      task.IsPhoto,
			MaxRetries:   1,
		}
		// Download and save the file
		golog.Debug("downloadTask", "opts", opts)
		_, err := gofile.DownloadAndSaveFile(opts)
		if err == nil {
			golog.Debug("downloadTask", "success", opts.URL, "savePaths", fullPaths)
			return nil
		}

		consecutiveFailures++
		lastErr = err
		golog.Debug("downloadTask", "error", err)
		golog.Warn("download failed, retrying",
			"url", task.URL,
			"attempt", i+1,
			"consecutiveFailures", consecutiveFailures,
			"error", err.Error())

		// If we have too many consecutive failures, fail fast to prevent memory accumulation
		if consecutiveFailures >= 2 {
			golog.Warn("Too many consecutive failures, failing fast to prevent memory issues",
				"url", task.URL, "consecutiveFailures", consecutiveFailures)
			break
		}

		// Exponential backoff with jitter to avoid thundering herd
		// Base delay: 2^attempt seconds, with max of 30 seconds (reduced from 60)
		baseDelay := time.Duration(1<<uint(i)) * time.Second
		if baseDelay > 30*time.Second {
			baseDelay = 30 * time.Second
		}
		// Add jitter (±25% of base delay)
		jitterFactor := float64(2*time.Now().UnixNano()%2 - 1) // -1 or +1
		jitter := time.Duration(float64(baseDelay) * 0.25 * jitterFactor)
		sleepTime := baseDelay + jitter

		golog.Debug("waiting before retry with exponential backoff",
			"sleepTime", sleepTime, "attempt", i+1, "baseDelay", baseDelay)
		time.Sleep(sleepTime)
	}
	// only record failed last attempt
	if err := d.recordFailedTask(task, id, lastErr, d.Config.MaxRetries); err != nil {
		golog.Error("failed to record failed task", "error", err)
	}
	return fmt.Errorf("failed after %d attempts: %w", d.Config.MaxRetries, lastErr)
}

// deleteTask handles a single deletion task
func (d *Downloader) deleteTask(task DeleteTask) error {
	for _, basePath := range d.StoragePaths {
		fullPath := filepath.Join(basePath, task.Path)

		// Check if path exists and is a file
		fileInfo, err := os.Stat(fullPath)
		if err != nil {
			if os.IsNotExist(err) {
				golog.Info("file does not exist, skipping deletion", "path", fullPath)
				continue
			}
			golog.Error("failed to stat file", "error", err, "path", fullPath)
			return fmt.Errorf("failed to stat file %s: %w", fullPath, err)
		}

		// Skip if it's a directory
		if fileInfo.IsDir() {
			golog.Info("path is a directory, skipping deletion", "path", fullPath)
			continue
		}

		// Delete the file
		if err := os.Remove(fullPath); err != nil {
			golog.Error("failed to delete file", "error", err, "path", fullPath)
			return fmt.Errorf("failed to delete file %s: %w", fullPath, err)
		}
	}
	return nil
}

// recordFailedTask records a failed download task in the database
func (d *Downloader) recordFailedTask(task MediaTask, id string, err error, retryCount int) error {
	failedTask := FailedTask{
		PropID:     id,
		ImageURL:   task.URL,
		ErrMsg:     err.Error(),
		RetryCount: retryCount,
	}
	if d.FailedCol == nil {
		return fmt.Errorf("FailedCol is nil, cannot record failed task")
	}
	_, err = d.FailedCol.InsertOne(context.Background(), failedTask)
	return err
}

// generateThumbnailAndUpdate generates a thumbnail and returns the hash value
func (d *Downloader) generateThumbnailAndUpdate(task MediaTask, oldTnLH int32) (int32, int, error) {
	var tnChangedNum int
	// Create thumbnail key
	thumbKey := task.MediaKey + "-t"
	sid := task.Sid

	// Generate hash and filename
	hash := levelStore.MurmurToInt32(thumbKey)
	golog.Debug("generateThumbnailAndUpdate", "hash", hash, "oldTnLH", oldTnLH)
	if hash == oldTnLH { // thumbnail not changed
		golog.Info("thumbnail already exists, skipping", "hash", hash)
		return hash, 0, nil
	}
	if oldTnLH != 0 && hash == 0 {
		tnChangedNum = -1
	}
	if oldTnLH == 0 && hash != 0 {
		tnChangedNum = 1
	}

	fileName, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to generate thumbnail filename: %w", err)
	}

	// Download and resize image
	newImg, err := gofile.DownloadAndResizeImage(task.URL, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to download and resize image: %w", err)
	}
	// Save thumbnail to all storage paths
	for _, basePath := range d.StoragePaths {
		// Get the directory of the original file
		dirPath := filepath.Dir(filepath.Join(basePath, task.DestPath))

		// Create the directory if it doesn't exist
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			return 0, 0, fmt.Errorf("failed to create directory: %w", err)
		}

		// delete old thumbnail
		if oldTnLH != 0 {
			fileNameOld, err := levelStore.Int32ToBase62(oldTnLH)
			if err != nil {
				golog.Error("failed to generate old thumbnail filename", "error", err, "oldTnLH", oldTnLH)
				return 0, 0, fmt.Errorf("failed to generate old thumbnail filename: %w", err)
			}
			filePathOld := filepath.Join(dirPath, sid+"_"+fileNameOld+".jpg")
			if _, err := os.Stat(filePathOld); err == nil {
				if err := os.Remove(filePathOld); err != nil {
					golog.Warn("failed to delete old thumbnail", "error", err, "path", filePathOld)
				}
			}
		}
		golog.Debug("generateThumbnailAndUpdate", "hash", hash, "fileName", fileName)
		// Save as JPEG in the same directory as the original file
		filePath := filepath.Join(dirPath, sid+"_"+fileName+".jpg")
		file, err := os.Create(filePath)
		if err != nil {
			return 0, 0, fmt.Errorf("failed to create thumbnail file: %w", err)
		}
		defer func() {
			if err := file.Close(); err != nil {
				golog.Error("failed to close thumbnail file", "error", err, "path", filePath)
			}
		}()

		if err := jpeg.Encode(file, newImg, &jpeg.Options{Quality: 100}); err != nil {
			return 0, 0, fmt.Errorf("failed to encode thumbnail: %w", err)
		}
	}
	return hash, tnChangedNum, nil
}
